.solutions {
  background-color: var(--color-bg-secondary);
}

.solutions-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
}

.solutions-header .section-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 1.5rem;
}

.solutions-header .section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.solution-card {
  position: relative;
  height: 400px;
  border-radius: 1rem;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.solution-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

/* Background patterns for different industries */
.commercial-bg {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
}

.residential-bg {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
}

.industrial-bg {
  background: linear-gradient(135deg, var(--color-accent), var(--color-accent-dark));
}

.healthcare-bg {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.hospitality-bg {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.education-bg {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.solution-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  transition: background-color 0.3s ease;
}

.solution-card:hover .solution-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.solution-content {
  text-align: center;
  color: var(--color-text-white);
  max-width: 100%;
}

.solution-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.solution-card:hover .solution-icon {
  transform: scale(1.1);
}

.solution-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-white);
}

.solution-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.solution-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.solution-features li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  padding-left: 1rem;
}

.solution-features li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: rgba(255, 255, 255, 0.6);
}

.solution-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-white);
  font-weight: 600;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.solution-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  gap: 0.75rem;
}

/* Solutions CTA */
.solutions-cta {
  background-color: var(--color-bg-primary);
  border-radius: 1.5rem;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cta-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

.cta-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-actions .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.cta-actions .btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-white);
}

.cta-actions .btn-primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.cta-actions .btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.cta-actions .btn-outline:hover {
  background-color: var(--color-primary);
  color: var(--color-text-white);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .solutions-header .section-title {
    font-size: 2.5rem;
  }
  
  .solutions-header .section-subtitle {
    font-size: 1.1rem;
  }
  
  .solutions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .solution-card {
    height: 350px;
  }
  
  .solution-overlay {
    padding: 1.5rem;
  }
  
  .solution-icon {
    width: 80px;
    height: 80px;
  }
  
  .solution-icon svg {
    width: 40px;
    height: 40px;
  }
  
  .solution-features {
    grid-template-columns: 1fr;
  }
  
  .solutions-cta {
    padding: 2rem 1rem;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-actions .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .solutions-header .section-title {
    font-size: 2rem;
  }
  
  .solution-title {
    font-size: 1.5rem;
  }
  
  .solution-description {
    font-size: 0.95rem;
  }
  
  .cta-content h3 {
    font-size: 1.75rem;
  }
  
  .cta-content p {
    font-size: 1rem;
  }
}
