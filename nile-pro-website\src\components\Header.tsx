import React, { useState } from 'react';
import { Menu, X, ChevronDown } from 'lucide-react';
import './Header.css';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <img src="/logo.png" alt="Nile Pro MEP" className="logo-img" />
            <div className="logo-text">
              <span className="company-name">Nile Pro</span>
              <span className="company-tagline">MEP Installation Works</span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="nav-desktop">
            <ul className="nav-list">
              <li className="nav-item">
                <a href="#home" className="nav-link">Home</a>
              </li>

              <li className="nav-item dropdown">
                <button
                  className="nav-link dropdown-toggle"
                  onClick={() => toggleDropdown('company')}
                >
                  Company <ChevronDown size={16} />
                </button>
                {activeDropdown === 'company' && (
                  <ul className="dropdown-menu">
                    <li><a href="#about">About Us</a></li>
                    <li><a href="#mission">Mission & Vision</a></li>
                    <li><a href="#team">Our Team</a></li>
                    <li><a href="#certifications">Certifications</a></li>
                  </ul>
                )}
              </li>

              <li className="nav-item dropdown">
                <button
                  className="nav-link dropdown-toggle"
                  onClick={() => toggleDropdown('services')}
                >
                  Services <ChevronDown size={16} />
                </button>
                {activeDropdown === 'services' && (
                  <ul className="dropdown-menu">
                    <li><a href="#hvac">HVAC Systems</a></li>
                    <li><a href="#electrical">Electrical Installation</a></li>
                    <li><a href="#plumbing">Plumbing Systems</a></li>
                    <li><a href="#fire-safety">Fire Safety Systems</a></li>
                    <li><a href="#automation">Building Automation</a></li>
                    <li><a href="#maintenance">Maintenance Services</a></li>
                  </ul>
                )}
              </li>

              <li className="nav-item dropdown">
                <button
                  className="nav-link dropdown-toggle"
                  onClick={() => toggleDropdown('solutions')}
                >
                  Solutions <ChevronDown size={16} />
                </button>
                {activeDropdown === 'solutions' && (
                  <ul className="dropdown-menu">
                    <li><a href="#commercial">Commercial Buildings</a></li>
                    <li><a href="#residential">Residential Projects</a></li>
                    <li><a href="#industrial">Industrial Facilities</a></li>
                    <li><a href="#healthcare">Healthcare Facilities</a></li>
                    <li><a href="#hospitality">Hospitality</a></li>
                    <li><a href="#education">Educational Buildings</a></li>
                  </ul>
                )}
              </li>

              <li className="nav-item">
                <a href="#projects" className="nav-link">Projects</a>
              </li>

              <li className="nav-item">
                <a href="#blog" className="nav-link">Insights</a>
              </li>

              <li className="nav-item">
                <a href="#contact" className="nav-link">Contact</a>
              </li>
            </ul>
          </nav>

          {/* Mobile Menu Button */}
          <button className="mobile-menu-btn" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="nav-mobile">
            <ul className="nav-mobile-list">
              <li><a href="#home" onClick={toggleMenu}>Home</a></li>
              <li><a href="#about" onClick={toggleMenu}>About Us</a></li>
              <li><a href="#services" onClick={toggleMenu}>Services</a></li>
              <li><a href="#solutions" onClick={toggleMenu}>Solutions</a></li>
              <li><a href="#projects" onClick={toggleMenu}>Projects</a></li>
              <li><a href="#blog" onClick={toggleMenu}>Insights</a></li>
              <li><a href="#contact" onClick={toggleMenu}>Contact</a></li>
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
