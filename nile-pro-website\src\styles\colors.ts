// Color palette for Nile Pro MEP - Professional construction/engineering colors
export const colors = {
  // Primary colors - Professional blue/navy (common in engineering/construction)
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Main primary
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  
  // Secondary colors - Professional orange/amber (energy, construction)
  secondary: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Main secondary
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  // Accent colors - Professional green (sustainability, efficiency)
  accent: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Main accent
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  // Neutral colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  // Semantic colors
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
  
  // Background colors
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    dark: '#1f2937',
  },
  
  // Text colors
  text: {
    primary: '#111827',
    secondary: '#6b7280',
    light: '#9ca3af',
    white: '#ffffff',
  }
};

// CSS custom properties for easy usage
export const cssVariables = `
  :root {
    --color-primary: ${colors.primary[500]};
    --color-primary-dark: ${colors.primary[700]};
    --color-primary-light: ${colors.primary[100]};
    
    --color-secondary: ${colors.secondary[500]};
    --color-secondary-dark: ${colors.secondary[700]};
    --color-secondary-light: ${colors.secondary[100]};
    
    --color-accent: ${colors.accent[500]};
    --color-accent-dark: ${colors.accent[700]};
    --color-accent-light: ${colors.accent[100]};
    
    --color-text-primary: ${colors.text.primary};
    --color-text-secondary: ${colors.text.secondary};
    --color-text-light: ${colors.text.light};
    --color-text-white: ${colors.text.white};
    
    --color-bg-primary: ${colors.background.primary};
    --color-bg-secondary: ${colors.background.secondary};
    --color-bg-dark: ${colors.background.dark};
    
    --color-success: ${colors.success};
    --color-warning: ${colors.warning};
    --color-error: ${colors.error};
    --color-info: ${colors.info};
  }
`;
