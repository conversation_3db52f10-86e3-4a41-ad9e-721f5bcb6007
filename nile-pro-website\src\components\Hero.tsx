import React from 'react';
import { ArrowRight, CheckCircle } from 'lucide-react';
import './Hero.css';

const Hero: React.FC = () => {
  return (
    <section className="hero" id="home">
      <div className="hero-background">
        <div className="hero-overlay"></div>
      </div>

      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title animate-fade-in-up">
              Excellence in MEP
              <span className="hero-title-highlight gradient-text"> Installation Works</span>
            </h1>

            <p className="hero-description animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              Combining advanced engineering expertise with reliable execution, our MEP solutions
              are built to deliver optimal performance and efficiency in every construction project.
              From HVAC systems to electrical and plumbing installations, we ensure excellence at every level.
            </p>

            <div className="hero-features animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <div className="hero-feature">
                <CheckCircle className="feature-icon animate-pulse" size={20} />
                <span>Professional MEP Design & Installation</span>
              </div>
              <div className="hero-feature">
                <CheckCircle className="feature-icon animate-pulse" size={20} />
                <span>Advanced HVAC & Electrical Systems</span>
              </div>
              <div className="hero-feature">
                <CheckCircle className="feature-icon animate-pulse" size={20} />
                <span>Comprehensive Project Management</span>
              </div>
            </div>

            <div className="hero-actions animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              <a href="#services" className="btn btn-secondary hero-btn hover-lift">
                Discover Our Services
                <ArrowRight size={20} />
              </a>
              <a href="#contact" className="btn btn-ghost hero-btn hover-lift">
                Get Quote
              </a>
            </div>
          </div>

          <div className="hero-stats animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            <div className="stat-item hover-lift animate-float" style={{ animationDelay: '0s' }}>
              <div className="stat-number">15+</div>
              <div className="stat-label">Years Experience</div>
            </div>
            <div className="stat-item hover-lift animate-float" style={{ animationDelay: '1s' }}>
              <div className="stat-number">200+</div>
              <div className="stat-label">Projects Completed</div>
            </div>
            <div className="stat-item hover-lift animate-float" style={{ animationDelay: '2s' }}>
              <div className="stat-number">50+</div>
              <div className="stat-label">Expert Engineers</div>
            </div>
            <div className="stat-item hover-lift animate-float" style={{ animationDelay: '3s' }}>
              <div className="stat-number">24/7</div>
              <div className="stat-label">Support Service</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
