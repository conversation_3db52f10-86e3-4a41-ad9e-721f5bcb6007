.contact {
  background-color: var(--color-bg-primary);
}

.contact-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
}

.contact-header .section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.contact-header .section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

/* Contact Information */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-info-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.contact-info-subtitle {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-cards {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--color-bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.contact-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-light);
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.contact-details {
  flex: 1;
}

.contact-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.contact-detail {
  color: var(--color-text-secondary);
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}

/* Map Container */
.map-container {
  margin-top: 2rem;
}

.map-placeholder {
  background: var(--color-bg-secondary);
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  text-align: center;
  color: var(--color-text-secondary);
}

.map-placeholder svg {
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.map-placeholder h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

/* Contact Form */
.contact-form-container {
  background: var(--color-bg-card);
  border-radius: var(--radius-2xl);
  padding: 2.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border);
}

.contact-form-header {
  margin-bottom: 2rem;
}

.contact-form-header h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.contact-form-header p {
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: 0.95rem;
}

.form-group label svg {
  color: var(--color-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem 1.25rem;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: inherit;
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--color-text-light);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1.25rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  margin-top: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Emergency Contact */
.emergency-contact {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
  border-radius: var(--radius-2xl);
  padding: 3rem 2rem;
  text-align: center;
  color: var(--color-text-white);
}

.emergency-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-white);
}

.emergency-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.emergency-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.25rem 2rem;
  background: var(--color-text-white);
  color: var(--color-secondary);
  font-weight: 700;
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.emergency-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
  background: var(--color-bg-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-header .section-title {
    font-size: 2.5rem;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .contact-form-container {
    padding: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .emergency-contact {
    padding: 2rem 1rem;
  }
  
  .emergency-content h3 {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .contact-header .section-title {
    font-size: 2rem;
  }
  
  .contact-form-container {
    padding: 1.5rem;
  }
  
  .contact-card {
    padding: 1rem;
  }
  
  .contact-icon {
    width: 40px;
    height: 40px;
  }
  
  .contact-icon svg {
    width: 18px;
    height: 18px;
  }
  
  .map-placeholder {
    padding: 2rem 1rem;
  }
  
  .emergency-content h3 {
    font-size: 1.5rem;
  }
}
