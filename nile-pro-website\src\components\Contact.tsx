import React, { useState } from 'react';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock,
  Send,
  User,
  MessageSquare,
  Building
} from 'lucide-react';
import './Contact.css';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // Reset form
    setFormData({
      name: '',
      email: '',
      company: '',
      phone: '',
      service: '',
      message: ''
    });
  };

  const contactInfo = [
    {
      icon: <MapPin size={24} />,
      title: "Our Location",
      details: ["Cairo, Egypt", "New Administrative Capital"],
      color: "var(--color-primary)"
    },
    {
      icon: <Phone size={24} />,
      title: "Phone Numbers",
      details: ["+20 XXX XXX XXXX", "+20 XXX XXX XXXX"],
      color: "var(--color-secondary)"
    },
    {
      icon: <Mail size={24} />,
      title: "Email Address",
      details: ["<EMAIL>", "<EMAIL>"],
      color: "var(--color-accent)"
    },
    {
      icon: <Clock size={24} />,
      title: "Working Hours",
      details: ["Sunday - Thursday: 8:00 AM - 6:00 PM", "24/7 Emergency Support"],
      color: "#8b5cf6"
    }
  ];

  const services = [
    "HVAC Systems",
    "Electrical Installation",
    "Plumbing Systems",
    "Fire Safety Systems",
    "Building Automation",
    "Maintenance Services",
    "Project Consultation",
    "Other"
  ];

  return (
    <section className="contact section" id="contact">
      <div className="container">
        <div className="contact-header">
          <h2 className="section-title gradient-text">Get In Touch</h2>
          <p className="section-subtitle">
            Ready to start your MEP project? Contact our expert team for consultation, 
            quotes, or any questions about our services. We're here to help bring your vision to life.
          </p>
        </div>

        <div className="contact-content">
          {/* Contact Information */}
          <div className="contact-info">
            <h3 className="contact-info-title">Contact Information</h3>
            <p className="contact-info-subtitle">
              Reach out to us through any of the following channels. 
              Our team is ready to assist you with your MEP needs.
            </p>
            
            <div className="contact-cards">
              {contactInfo.map((info, index) => (
                <div 
                  key={index} 
                  className="contact-card animate-fade-in-left"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <div className="contact-icon" style={{color: info.color}}>
                    {info.icon}
                  </div>
                  <div className="contact-details">
                    <h4 className="contact-title">{info.title}</h4>
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="contact-detail">{detail}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Map Placeholder */}
            <div className="map-container">
              <div className="map-placeholder">
                <MapPin size={48} />
                <h4>Find Us Here</h4>
                <p>Interactive map will be integrated here</p>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-form-container">
            <div className="contact-form-header">
              <h3>Send Us a Message</h3>
              <p>Fill out the form below and we'll get back to you within 24 hours.</p>
            </div>
            
            <form className="contact-form" onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">
                    <User size={18} />
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">
                    <Mail size={18} />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter your email address"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="company">
                    <Building size={18} />
                    Company Name
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="Enter your company name"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="phone">
                    <Phone size={18} />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="service">
                  Service Required
                </label>
                <select
                  id="service"
                  name="service"
                  value={formData.service}
                  onChange={handleInputChange}
                >
                  <option value="">Select a service</option>
                  {services.map((service, index) => (
                    <option key={index} value={service}>{service}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="message">
                  <MessageSquare size={18} />
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={5}
                  placeholder="Tell us about your project requirements..."
                ></textarea>
              </div>

              <button type="submit" className="btn btn-primary submit-btn">
                <Send size={20} />
                Send Message
              </button>
            </form>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="emergency-contact">
          <div className="emergency-content">
            <h3>Emergency MEP Services</h3>
            <p>
              Need urgent MEP assistance? Our emergency response team is available 24/7 
              for critical system failures and urgent repairs.
            </p>
            <a href="tel:+20XXXXXXXXX" className="btn btn-secondary emergency-btn">
              <Phone size={20} />
              Call Emergency Line
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
