import React from 'react';
import { 
  Building2, 
  Home, 
  Factory, 
  Heart, 
  Hotel,
  GraduationCap,
  ArrowRight 
} from 'lucide-react';
import './Solutions.css';

const Solutions: React.FC = () => {
  const solutions = [
    {
      icon: <Building2 size={50} />,
      title: "Commercial Buildings",
      description: "Comprehensive MEP solutions for office buildings, retail spaces, and commercial complexes with focus on energy efficiency and occupant comfort.",
      features: ["Office Buildings", "Shopping Centers", "Business Parks", "Mixed-Use Developments"],
      image: "commercial-bg"
    },
    {
      icon: <Home size={50} />,
      title: "Residential Projects",
      description: "Tailored MEP installations for residential developments, ensuring comfort, safety, and energy efficiency for modern living spaces.",
      features: ["Apartment Complexes", "Villas & Houses", "Gated Communities", "Luxury Residences"],
      image: "residential-bg"
    },
    {
      icon: <Factory size={50} />,
      title: "Industrial Facilities",
      description: "Specialized MEP systems for industrial environments, designed to meet demanding operational requirements and safety standards.",
      features: ["Manufacturing Plants", "Warehouses", "Processing Facilities", "Distribution Centers"],
      image: "industrial-bg"
    },
    {
      icon: <Heart size={50} />,
      title: "Healthcare Facilities",
      description: "Critical MEP systems for healthcare environments, ensuring patient safety, infection control, and regulatory compliance.",
      features: ["Hospitals", "Medical Centers", "Clinics", "Laboratory Facilities"],
      image: "healthcare-bg"
    },
    {
      icon: <Hotel size={50} />,
      title: "Hospitality",
      description: "Premium MEP solutions for hospitality sector, focusing on guest comfort, energy efficiency, and operational excellence.",
      features: ["Hotels & Resorts", "Restaurants", "Entertainment Venues", "Conference Centers"],
      image: "hospitality-bg"
    },
    {
      icon: <GraduationCap size={50} />,
      title: "Educational Buildings",
      description: "MEP systems designed for educational environments, promoting healthy learning spaces with optimal comfort and safety.",
      features: ["Schools & Universities", "Training Centers", "Libraries", "Research Facilities"],
      image: "education-bg"
    }
  ];

  return (
    <section className="solutions section" id="solutions">
      <div className="container">
        <div className="solutions-header">
          <h2 className="section-title">MEP Solutions by Industry</h2>
          <p className="section-subtitle">
            At Nile Pro, we offer tailored MEP solutions for a wide range of building types and industries. 
            Whether it's commercial buildings, healthcare facilities, industrial plants, or residential projects, 
            we understand that each environment has unique requirements. Our systems are designed to provide 
            efficient and reliable performance in every space. Explore our solutions to find the best fit for your project.
          </p>
        </div>

        <div className="solutions-grid">
          {solutions.map((solution, index) => (
            <div key={index} className={`solution-card ${solution.image}`}>
              <div className="solution-overlay">
                <div className="solution-content">
                  <div className="solution-icon">
                    {solution.icon}
                  </div>
                  
                  <h3 className="solution-title">{solution.title}</h3>
                  <p className="solution-description">{solution.description}</p>
                  
                  <ul className="solution-features">
                    {solution.features.map((feature, featureIndex) => (
                      <li key={featureIndex}>{feature}</li>
                    ))}
                  </ul>
                  
                  <a href="#contact" className="solution-link">
                    Learn More <ArrowRight size={16} />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="solutions-cta">
          <div className="cta-content">
            <h3>Custom Solutions for Your Industry</h3>
            <p>
              Every industry has unique MEP requirements. Our experienced team works closely with clients 
              to develop customized solutions that meet specific industry standards, regulations, and operational needs.
            </p>
            <div className="cta-actions">
              <a href="#contact" className="btn btn-primary">
                Discuss Your Project
              </a>
              <a href="#projects" className="btn btn-outline">
                View Our Work
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Solutions;
