import React from 'react';
import { 
  Wind, 
  Zap, 
  Droplets, 
  Flame, 
  Settings, 
  Shield,
  ArrowRight 
} from 'lucide-react';
import './Services.css';

const Services: React.FC = () => {
  const services = [
    {
      icon: <Wind size={40} />,
      title: "HVAC Systems",
      description: "Complete heating, ventilation, and air conditioning solutions for optimal indoor climate control and energy efficiency.",
      features: ["Air Handling Units", "Ductwork Installation", "Climate Control Systems", "Energy Recovery Units"]
    },
    {
      icon: <Zap size={40} />,
      title: "Electrical Installation",
      description: "Professional electrical systems design and installation ensuring safety, reliability, and compliance with all regulations.",
      features: ["Power Distribution", "Lighting Systems", "Emergency Power", "Control Panels"]
    },
    {
      icon: <Droplets size={40} />,
      title: "Plumbing Systems",
      description: "Comprehensive plumbing solutions including water supply, drainage, and specialized piping systems for all building types.",
      features: ["Water Supply Systems", "Drainage Solutions", "Pipe Installation", "Fixture Installation"]
    },
    {
      icon: <Flame size={40} />,
      title: "Fire Safety Systems",
      description: "Advanced fire protection and safety systems to ensure building compliance and occupant safety in emergency situations.",
      features: ["Fire Sprinkler Systems", "Fire Alarm Systems", "Emergency Lighting", "Smoke Detection"]
    },
    {
      icon: <Settings size={40} />,
      title: "Building Automation",
      description: "Smart building solutions integrating MEP systems for enhanced efficiency, monitoring, and automated control.",
      features: ["BMS Integration", "Smart Controls", "Energy Management", "System Monitoring"]
    },
    {
      icon: <Shield size={40} />,
      title: "Maintenance Services",
      description: "Comprehensive maintenance and support services to ensure optimal performance and longevity of all MEP systems.",
      features: ["Preventive Maintenance", "Emergency Repairs", "System Upgrades", "24/7 Support"]
    }
  ];

  return (
    <section className="services section" id="services">
      <div className="container">
        <div className="services-header">
          <h2 className="section-title">MEP Services</h2>
          <p className="section-subtitle">
            At Nile Pro, we offer a comprehensive range of MEP services that provide high performance 
            and reliability. Our services, from HVAC systems to electrical and plumbing installations, 
            are designed for different environments to ensure the best building performance. 
            Explore our MEP service range below to find the right fit for your project needs.
          </p>
        </div>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-icon">
                {service.icon}
              </div>
              
              <div className="service-content">
                <h3 className="service-title">{service.title}</h3>
                <p className="service-description">{service.description}</p>
                
                <ul className="service-features">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex}>{feature}</li>
                  ))}
                </ul>
                
                <a href="#contact" className="service-link">
                  Learn More <ArrowRight size={16} />
                </a>
              </div>
            </div>
          ))}
        </div>

        <div className="services-cta">
          <div className="cta-content">
            <h3>Need a Custom MEP Solution?</h3>
            <p>
              Our expert team can design and implement tailored MEP solutions 
              to meet your specific project requirements and industry standards.
            </p>
            <a href="#contact" className="btn btn-primary">
              Get Custom Quote
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
