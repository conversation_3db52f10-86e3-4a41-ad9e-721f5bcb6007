.services {
  background-color: var(--color-bg-primary);
}

.services-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
}

.services-header .section-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 1.5rem;
}

.services-header .section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.service-card {
  background-color: var(--color-bg-primary);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--color-bg-secondary);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 1rem;
  color: var(--color-text-white);
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.service-description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.service-features li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.95rem;
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-accent);
  font-weight: bold;
}

.service-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  margin-top: auto;
}

.service-link:hover {
  color: var(--color-primary-dark);
  gap: 0.75rem;
}

/* Services CTA */
.services-cta {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 1.5rem;
  padding: 3rem 2rem;
  text-align: center;
  color: var(--color-text-white);
}

.cta-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-white);
}

.cta-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-content .btn {
  background-color: var(--color-secondary);
  color: var(--color-text-white);
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.cta-content .btn:hover {
  background-color: var(--color-secondary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .services-header .section-title {
    font-size: 2.5rem;
  }
  
  .services-header .section-subtitle {
    font-size: 1.1rem;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .service-card {
    padding: 1.5rem;
  }
  
  .service-icon {
    width: 60px;
    height: 60px;
  }
  
  .service-icon svg {
    width: 30px;
    height: 30px;
  }
  
  .services-cta {
    padding: 2rem 1rem;
  }
  
  .cta-content h3 {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .services-header .section-title {
    font-size: 2rem;
  }
  
  .service-title {
    font-size: 1.25rem;
  }
  
  .service-description {
    font-size: 0.95rem;
  }
  
  .cta-content h3 {
    font-size: 1.5rem;
  }
  
  .cta-content p {
    font-size: 1rem;
  }
}
