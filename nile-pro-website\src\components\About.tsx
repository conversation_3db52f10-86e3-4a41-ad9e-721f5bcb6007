import React from 'react';
import { Award, Users, Wrench, Clock } from 'lucide-react';
import './About.css';

const About: React.FC = () => {
  return (
    <section className="about section" id="about">
      <div className="container">
        <div className="about-content">
          {/* Main About Content */}
          <div className="about-main">
            <div className="about-text">
              <h2 className="section-title">
                15+ Years of MEP Excellence
              </h2>
              <p className="section-subtitle">
                Established as a leading MEP contractor, Nile Pro for Construction specializes in 
                Mechanical, Electrical, and Plumbing installation works. We have built our reputation 
                on delivering high-quality, efficient, and reliable MEP solutions for construction projects 
                across various industries.
              </p>
              
              <div className="about-highlights">
                <div className="highlight-item">
                  <h4>Our Mission</h4>
                  <p>
                    To provide exceptional MEP installation services that exceed client expectations 
                    through innovative solutions, professional expertise, and unwavering commitment to quality.
                  </p>
                </div>
                
                <div className="highlight-item">
                  <h4>Our Vision</h4>
                  <p>
                    To be the most trusted MEP contractor in the region, known for our technical excellence, 
                    reliability, and contribution to sustainable building practices.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="about-image">
              <div className="image-placeholder">
                <div className="image-content">
                  <Wrench size={80} className="placeholder-icon" />
                  <p>Professional MEP Installation</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Features Grid */}
          <div className="about-features">
            <div className="feature-card">
              <div className="feature-icon">
                <Award size={40} />
              </div>
              <h3>Quality Assurance</h3>
              <p>
                Certified professionals ensuring every installation meets the highest industry standards 
                and regulatory requirements.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">
                <Users size={40} />
              </div>
              <h3>Expert Team</h3>
              <p>
                Skilled engineers and technicians with extensive experience in complex MEP systems 
                and modern installation techniques.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">
                <Wrench size={40} />
              </div>
              <h3>Comprehensive Services</h3>
              <p>
                Full-spectrum MEP solutions from design consultation to installation, testing, 
                and ongoing maintenance support.
              </p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">
                <Clock size={40} />
              </div>
              <h3>Timely Delivery</h3>
              <p>
                Efficient project management ensuring on-time completion without compromising 
                on quality or safety standards.
              </p>
            </div>
          </div>
          
          {/* Company Stats */}
          <div className="company-stats">
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-number">200+</div>
                <div className="stat-label">Projects Completed</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">50+</div>
                <div className="stat-label">Expert Engineers</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">15+</div>
                <div className="stat-label">Years Experience</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">100%</div>
                <div className="stat-label">Client Satisfaction</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
