.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid var(--color-border-light);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

/* Logo Styles */
.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-img {
  height: 50px;
  width: auto;
  object-fit: contain;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  line-height: 1.2;
}

.company-tagline {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* Desktop Navigation */
.nav-desktop {
  display: flex;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0;
  color: var(--color-text-primary);
  font-weight: 500;
  text-decoration: none;
  transition: color 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-family: inherit;
}

.nav-link:hover {
  color: var(--color-primary);
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-2xl);
  border-radius: var(--radius-xl);
  padding: 0.75rem 0;
  min-width: 220px;
  list-style: none;
  margin: 0;
  z-index: 1001;
  border: 1px solid var(--color-border-light);
  opacity: 0;
  transform: translateY(-10px);
  animation: dropdownFadeIn 0.3s ease forwards;
}

@keyframes dropdownFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu a {
  display: block;
  padding: 0.875rem 1.25rem;
  color: var(--color-text-primary);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  border-radius: var(--radius-md);
  margin: 0 0.5rem;
  position: relative;
}

.dropdown-menu a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 0;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
  transition: height 0.3s ease;
}

.dropdown-menu a:hover::before {
  height: 60%;
}

.dropdown-menu a:hover {
  background-color: var(--color-primary-50);
  color: var(--color-primary);
  transform: translateX(8px);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--color-text-primary);
  cursor: pointer;
  padding: 0.5rem;
}

/* Mobile Navigation */
.nav-mobile {
  display: none;
  background-color: var(--color-bg-primary);
  border-top: 1px solid var(--color-bg-secondary);
  padding: 1rem 0;
}

.nav-mobile-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-mobile-list li {
  margin: 0;
}

.nav-mobile-list a {
  display: block;
  padding: 0.75rem 0;
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid var(--color-bg-secondary);
  transition: color 0.3s ease;
}

.nav-mobile-list a:hover {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .nav-mobile {
    display: block;
  }

  .logo-text {
    display: none;
  }

  .logo-img {
    height: 40px;
  }

  .header-content {
    padding: 0.75rem 0;
  }
}

@media (max-width: 480px) {
  .company-name {
    font-size: 1.25rem;
  }

  .company-tagline {
    font-size: 0.75rem;
  }
}