.header {
  background-color: var(--color-bg-primary);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

/* Logo Styles */
.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-img {
  height: 50px;
  width: auto;
  object-fit: contain;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  line-height: 1.2;
}

.company-tagline {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* Desktop Navigation */
.nav-desktop {
  display: flex;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0;
  color: var(--color-text-primary);
  font-weight: 500;
  text-decoration: none;
  transition: color 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-family: inherit;
}

.nav-link:hover {
  color: var(--color-primary);
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--color-bg-primary);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 200px;
  list-style: none;
  margin: 0;
  z-index: 1001;
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu a {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--color-text-primary);
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-menu a:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-primary);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--color-text-primary);
  cursor: pointer;
  padding: 0.5rem;
}

/* Mobile Navigation */
.nav-mobile {
  display: none;
  background-color: var(--color-bg-primary);
  border-top: 1px solid var(--color-bg-secondary);
  padding: 1rem 0;
}

.nav-mobile-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-mobile-list li {
  margin: 0;
}

.nav-mobile-list a {
  display: block;
  padding: 0.75rem 0;
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid var(--color-bg-secondary);
  transition: color 0.3s ease;
}

.nav-mobile-list a:hover {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .nav-mobile {
    display: block;
  }
  
  .logo-text {
    display: none;
  }
  
  .logo-img {
    height: 40px;
  }
  
  .header-content {
    padding: 0.75rem 0;
  }
}

@media (max-width: 480px) {
  .company-name {
    font-size: 1.25rem;
  }
  
  .company-tagline {
    font-size: 0.75rem;
  }
}
