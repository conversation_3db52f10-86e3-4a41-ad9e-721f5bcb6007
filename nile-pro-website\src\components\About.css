.about {
  background-color: var(--color-bg-secondary);
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

/* Main About Section */
.about-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1.2;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
  margin-bottom: 2rem;
}

.about-highlights {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.highlight-item h4 {
  color: var(--color-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.highlight-item p {
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 100%;
  height: 400px;
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-secondary-light));
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-primary);
}

.image-content {
  text-align: center;
  color: var(--color-primary);
}

.placeholder-icon {
  margin-bottom: 1rem;
}

.image-content p {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-primary);
}

/* Features Grid */
.about-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--color-bg-card);
  padding: 2.5rem 2rem;
  border-radius: var(--radius-2xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary), var(--color-accent));
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--color-primary-light);
}

.feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 90px;
  height: 90px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-2xl);
  color: var(--color-text-white);
  margin-bottom: 2rem;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-lg);
}

.feature-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent));
  border-radius: var(--radius-2xl);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.feature-card:hover .feature-icon::before {
  opacity: 1;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.feature-card p {
  color: var(--color-text-secondary);
  line-height: 1.6;
}

/* Company Stats */
.company-stats {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 1.5rem;
  padding: 3rem 2rem;
  color: var(--color-text-white);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-card {
  text-align: center;
  padding: 1rem;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--color-secondary);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-subtitle {
    font-size: 1.1rem;
  }

  .about-features {
    grid-template-columns: 1fr;
  }

  .image-placeholder {
    height: 300px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .company-stats {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 2rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
  }

  .feature-icon svg {
    width: 30px;
    height: 30px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}