.projects {
  background-color: var(--color-bg-primary);
}

.projects-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.projects-header .section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.projects-header .section-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

/* Filter <PERSON> */
.projects-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.filter-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--color-bg-card);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.filter-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-color: var(--color-primary);
  color: var(--color-text-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.project-card {
  background: var(--color-bg-card);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-border);
}

.project-card:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--color-primary-light);
}

/* Project Image */
.project-image {
  position: relative;
  height: 250px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  overflow: hidden;
}

.commercial-project-1 {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
}

.residential-project-1 {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
}

.healthcare-project-1 {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.hospitality-project-1 {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.industrial-project-1 {
  background: linear-gradient(135deg, var(--color-accent), var(--color-accent-dark));
}

.education-project-1 {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
}

.project-category {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: var(--color-text-white);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Project Content */
.project-content {
  padding: 2rem;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.project-description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.project-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-muted);
  font-size: 0.9rem;
}

.project-detail svg {
  color: var(--color-primary);
}

.project-services {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.service-tag {
  background: var(--color-primary-50);
  color: var(--color-primary);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid var(--color-primary-light);
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.project-link:hover {
  color: var(--color-primary-dark);
  gap: 0.75rem;
}

/* Projects CTA */
.projects-cta {
  background: linear-gradient(135deg, var(--color-bg-secondary), var(--color-bg-tertiary));
  border-radius: var(--radius-2xl);
  padding: 3rem 2rem;
  text-align: center;
  border: 1px solid var(--color-border);
}

.cta-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

.cta-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-actions .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects-header .section-title {
    font-size: 2.5rem;
  }
  
  .projects-filters {
    gap: 0.5rem;
  }
  
  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .project-content {
    padding: 1.5rem;
  }
  
  .projects-cta {
    padding: 2rem 1rem;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-actions .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .projects-header .section-title {
    font-size: 2rem;
  }
  
  .project-title {
    font-size: 1.25rem;
  }
  
  .project-image {
    height: 200px;
  }
  
  .cta-content h3 {
    font-size: 1.75rem;
  }
}
