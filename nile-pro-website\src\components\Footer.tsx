import React from 'react';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock,
  Linkedin,
  Facebook,
  Twitter
} from 'lucide-react';
import './Footer.css';

const Footer: React.FC = () => {
  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          {/* Company Info */}
          <div className="footer-section">
            <div className="footer-logo">
              <img src="/logo.jpg" alt="Nile Pro MEP" className="footer-logo-img" />
              <div className="footer-logo-text">
                <span className="footer-company-name">Nile Pro</span>
                <span className="footer-company-tagline">MEP Installation Works</span>
              </div>
            </div>
            <p className="footer-description">
              Leading MEP contractor specializing in Mechanical, Electrical, and Plumbing 
              installation works for construction projects across various industries.
            </p>
            <div className="footer-social">
              <a href="#" className="social-link">
                <Linkedin size={20} />
              </a>
              <a href="#" className="social-link">
                <Facebook size={20} />
              </a>
              <a href="#" className="social-link">
                <Twitter size={20} />
              </a>
            </div>
          </div>

          {/* Services */}
          <div className="footer-section">
            <h3 className="footer-title">Services</h3>
            <ul className="footer-links">
              <li><a href="#hvac">HVAC Systems</a></li>
              <li><a href="#electrical">Electrical Installation</a></li>
              <li><a href="#plumbing">Plumbing Systems</a></li>
              <li><a href="#fire-safety">Fire Safety Systems</a></li>
              <li><a href="#automation">Building Automation</a></li>
              <li><a href="#maintenance">Maintenance Services</a></li>
            </ul>
          </div>

          {/* Solutions */}
          <div className="footer-section">
            <h3 className="footer-title">Solutions</h3>
            <ul className="footer-links">
              <li><a href="#commercial">Commercial Buildings</a></li>
              <li><a href="#residential">Residential Projects</a></li>
              <li><a href="#industrial">Industrial Facilities</a></li>
              <li><a href="#healthcare">Healthcare Facilities</a></li>
              <li><a href="#hospitality">Hospitality</a></li>
              <li><a href="#education">Educational Buildings</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h3 className="footer-title">Contact Info</h3>
            <div className="contact-info">
              <div className="contact-item">
                <MapPin size={18} />
                <span>Cairo, Egypt</span>
              </div>
              <div className="contact-item">
                <Phone size={18} />
                <span>+20 XXX XXX XXXX</span>
              </div>
              <div className="contact-item">
                <Mail size={18} />
                <span><EMAIL></span>
              </div>
              <div className="contact-item">
                <Clock size={18} />
                <span>24/7 Support Available</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; 2024 Nile Pro for Construction - MEP Installation Works. All Rights Reserved.</p>
            <div className="footer-bottom-links">
              <a href="#privacy">Privacy Policy</a>
              <a href="#terms">Terms of Service</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
