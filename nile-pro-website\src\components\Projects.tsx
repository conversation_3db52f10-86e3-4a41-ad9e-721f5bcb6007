import React, { useState } from 'react';
import { 
  Building2, 
  Home, 
  Factory, 
  Heart, 
  Hotel,
  GraduationCap,
  MapPin,
  Calendar,
  Users,
  ArrowRight
} from 'lucide-react';
import './Projects.css';

const Projects: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: "Cairo Business District Tower",
      category: "commercial",
      location: "New Cairo, Egypt",
      year: "2024",
      client: "Cairo Development Corp",
      description: "Complete MEP installation for 40-story commercial tower including advanced HVAC, electrical systems, and smart building automation.",
      services: ["HVAC Systems", "Electrical Installation", "Building Automation", "Fire Safety"],
      image: "commercial-project-1"
    },
    {
      id: 2,
      title: "Nile View Residential Complex",
      category: "residential",
      location: "Maadi, Cairo",
      year: "2023",
      client: "Nile Properties",
      description: "MEP systems for luxury residential complex with 200 units, featuring energy-efficient solutions and modern amenities.",
      services: ["HVAC Systems", "Electrical Installation", "Plumbing Systems", "Smart Home Integration"],
      image: "residential-project-1"
    },
    {
      id: 3,
      title: "Alexandria Medical Center",
      category: "healthcare",
      location: "Alexandria, Egypt",
      year: "2024",
      client: "Alexandria Health Authority",
      description: "Critical MEP systems for 300-bed hospital including specialized HVAC for operating rooms and medical gas systems.",
      services: ["Medical HVAC", "Emergency Power", "Medical Gas Systems", "Fire Safety"],
      image: "healthcare-project-1"
    },
    {
      id: 4,
      title: "Red Sea Resort & Spa",
      category: "hospitality",
      location: "Hurghada, Egypt",
      year: "2023",
      client: "Red Sea Tourism",
      description: "Comprehensive MEP installation for luxury resort with 150 rooms, spa facilities, and conference center.",
      services: ["HVAC Systems", "Pool Systems", "Electrical Installation", "Building Automation"],
      image: "hospitality-project-1"
    },
    {
      id: 5,
      title: "Cairo Manufacturing Plant",
      category: "industrial",
      location: "10th of Ramadan City",
      year: "2024",
      client: "Egyptian Industries Ltd",
      description: "Industrial MEP systems for manufacturing facility including specialized ventilation and power distribution.",
      services: ["Industrial HVAC", "High Voltage Systems", "Process Cooling", "Safety Systems"],
      image: "industrial-project-1"
    },
    {
      id: 6,
      title: "Future University Campus",
      category: "education",
      location: "New Cairo, Egypt",
      year: "2023",
      client: "Future University Egypt",
      description: "MEP systems for university campus including lecture halls, laboratories, and administrative buildings.",
      services: ["HVAC Systems", "Electrical Installation", "Lab Ventilation", "Smart Campus Systems"],
      image: "education-project-1"
    }
  ];

  const categories = [
    { id: 'all', name: 'All Projects', icon: <Building2 size={20} /> },
    { id: 'commercial', name: 'Commercial', icon: <Building2 size={20} /> },
    { id: 'residential', name: 'Residential', icon: <Home size={20} /> },
    { id: 'healthcare', name: 'Healthcare', icon: <Heart size={20} /> },
    { id: 'hospitality', name: 'Hospitality', icon: <Hotel size={20} /> },
    { id: 'industrial', name: 'Industrial', icon: <Factory size={20} /> },
    { id: 'education', name: 'Education', icon: <GraduationCap size={20} /> }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <section className="projects section" id="projects">
      <div className="container">
        <div className="projects-header">
          <h2 className="section-title gradient-text">Our Projects</h2>
          <p className="section-subtitle">
            Explore our portfolio of successful MEP installations across various industries. 
            Each project showcases our commitment to excellence, innovation, and client satisfaction.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="projects-filters">
          {categories.map((category) => (
            <button
              key={category.id}
              className={`filter-btn ${activeFilter === category.id ? 'active' : ''}`}
              onClick={() => setActiveFilter(category.id)}
            >
              {category.icon}
              {category.name}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="projects-grid">
          {filteredProjects.map((project, index) => (
            <div 
              key={project.id} 
              className="project-card hover-lift animate-fade-in-up"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              <div className={`project-image ${project.image}`}>
                <div className="project-overlay">
                  <div className="project-category">{project.category}</div>
                </div>
              </div>
              
              <div className="project-content">
                <h3 className="project-title">{project.title}</h3>
                <p className="project-description">{project.description}</p>
                
                <div className="project-details">
                  <div className="project-detail">
                    <MapPin size={16} />
                    <span>{project.location}</span>
                  </div>
                  <div className="project-detail">
                    <Calendar size={16} />
                    <span>{project.year}</span>
                  </div>
                  <div className="project-detail">
                    <Users size={16} />
                    <span>{project.client}</span>
                  </div>
                </div>
                
                <div className="project-services">
                  {project.services.map((service, serviceIndex) => (
                    <span key={serviceIndex} className="service-tag">
                      {service}
                    </span>
                  ))}
                </div>
                
                <a href="#contact" className="project-link">
                  View Details <ArrowRight size={16} />
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="projects-cta">
          <div className="cta-content">
            <h3>Ready to Start Your Project?</h3>
            <p>
              Let's discuss how we can bring your MEP vision to life with our proven expertise 
              and commitment to excellence.
            </p>
            <div className="cta-actions">
              <a href="#contact" className="btn btn-primary">
                Start Your Project
              </a>
              <a href="#services" className="btn btn-outline">
                Our Services
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
